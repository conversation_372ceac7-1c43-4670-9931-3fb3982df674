

# **面向大规模MIMO系统的AI使能SVD算子高性能实现战略蓝图**

## **第一部分：解构问题领域：非理想无线信道中的SVD**

本节旨在建立对问题的基本认知。它将超越对问题描述的表面解读，深入探究其背后的物理原理、数学约束以及评估指标的实践意义。

### **1.1 SVD的物理与数学必要性**

奇异值分解（Singular Value Decomposition, SVD）是无线通信领域的一项基础性技术。在单用户多输入多输出（MIMO）系统中，其核心作用在于预编码设计 1。信道矩阵

H可以被分解为H=UΣV∗，其中U和V是酉矩阵，Σ是对角矩阵。该分解允许将一个复杂的、耦合的MIMO信道转变为一组并行的、相互独立的子信道。这一过程被称为信道对角化，是消除数据流间干扰、实现最大空间复用增益的关键 1。因此，SVD为最优线性预编码方案提供了理论上的“黄金标准”，AI模型必须学会近似这一过程。

然而，随着无线系统向大规模MIMO（Massive MIMO）乃至超大规模MIMO（XL-MIMO）演进，天线数量呈数量级增长 1。传统SVD算法（如Golub-Kahan-Reinsch算法）的计算复杂度随矩阵维度呈立方级增长，约为

O(min(M,N)⋅M⋅N)。当天线数M和N增长至数百甚至数千时，这种高复杂度会导致无法接受的处理时延和功耗，使其不适用于基站的实时信号处理 1。这构成了寻求基于AI的近似算法的核心动机。

### **1.2 评估指令：一个多目标优化挑战**

本次竞赛的评估标准不仅衡量算法的准确性，还兼顾其输出的数学结构特性，构成了一个复杂的多目标优化问题。

#### **1.2.1 近似误差（AE）公式的深度剖析**

竞赛采用的近似误差（Approximation Error, AE）公式为 1：

AEi​=∥Hlabeli​​∥F​∥Hlabeli​​−Ui​Σi​Vi∗​∥F​​+∥Ui∗​Ui​−I∥F​+∥Vi∗​Vi​−I∥F​  
该公式由三个部分组成：

1. **重构保真度**：第一项是重构误差的归一化Frobenius范数。这是一个标准的矩阵回归损失。关键在于，模型使用*非理想*信道H作为输入，但评估时却以*理想*信道$H\_{\\text{label}}$为基准 1。这意味着模型除了执行分解任务，还必须隐式地学习去噪和信道均衡。  
2. **正交性惩罚**：第二项和第三项是惩罚输出矩阵U和V偏离酉矩阵（或实数域下的正交矩阵）的程度。Frobenius范数$|A^\*A \- I|\_F$是深度学习中一种常见的“软约束”，用于鼓励矩阵的正交性 3。

#### **1.2.2 将问题视为多任务学习**

AE公式本质上并非单一目标，而是三个不同目标的加权和：1) 重构Hlabel​；2) 使U成为酉矩阵；3) 使V成为酉矩阵。

若采用简单地最小化这三项之和的朴素训练方法，很可能会得到次优解。如果各项损失的量级存在显著差异（例如，重构误差通常远大于正交性误差），那么量级较大的损失项的梯度将在学习过程中占据主导地位。这可能导致网络虽然能较好地重构信道，但输出的奇异向量却严重偏离正交性要求。因此，该问题应被视为一个多任务学习（Multi-Task Learning, MTL）挑战，平衡各任务对于获得较低的综合AE至关重要。这一认知直接指导了第三部分中高级训练策略的设计。

### **1.3 效率约束：从理论精度到实践可行性**

乘加运算次数（Multiply-Accumulate, MACs）是本次竞赛的第二项评估指标 1。这并非一个学术指标，而是计算复杂度的直接体现，它直接关系到模型在基站专用硬件（如ASIC、FPGA）上的推理延迟和功耗。

由于最终排名由AE和MACs共同决定，这意味着存在一个性能权衡。一个庞大、高精度的模型可能会输给一个精度稍低但效率远超其右的模型。这启发了一种两阶段的优化策略：

* **第一阶段：最大化精度**。初期，应专注于最小化AE分数，可以采用一个更大、表达能力更强的模型架构，暂时不必过于关注MACs。此阶段的目标是确定模型性能的上限。  
* **第二阶段：最优化效率**。在获得高精度模型后，应用模型压缩技术（详见第四部分）来降低MACs，同时密切监控其对AE的影响。这种结构化的方法比从一开始就试图同时优化两个目标，更有可能在精度与效率的帕累托前沿上找到最佳点。这一思路也得到了模型压缩领域研究的支持，该领域通常涉及对预训练的大模型进行微调 5。

---

## **第二部分：最先进SVD算子的架构蓝图**

本节将从理解问题转向设计核心解决方案——神经网络本身。我们将提出一个具体的、新颖的架构，并基于对相关研究的综合分析来论证其设计选择。

### **2.1 基础范式：卷积与注意力的对比**

* **CNN用于空间特征提取**：Peken等人的研究表明，卷积神经网络（CNN）可用于近似SVD 2。CNN天然适用于处理如信道矩阵  
  H这样的网格状数据。其卷积核作为可学习的特征提取器，能够识别局部模式（如信道路径簇），其层次化结构则允许将这些特征聚合成更抽象的表示。竞赛背景资料1中对该文献的引用也凸显了其相关性。  
* **Transformer用于全局上下文**：Charton F.的研究将Transformer应用于线性代数任务 7。Transformer自注意力机制的关键优势在于其能够建模长距离依赖关系，将整个矩阵视为一个全局上下文。然而，研究也揭示了其显著挑战：将矩阵编码为序列的困难性，以及小型Transformer在处理仅5x5的SVD问题时便已失效 8，这表明其对复杂度和数据量的要求极高。

综合来看，纯CNN架构可能难以捕捉分解任务所需的全局、整体性矩阵属性。而纯Transformer架构则忽略了信道矩阵强烈的二维空间局部性，并且其自注意力机制的二次方复杂度与MACs效率约束相悖 9。因此，一个结合了CNN的局部特征提取能力与注意力机制的全局上下文建模能力的混合模型，展现出最有希望的路径。这一综合思路在更广泛的计算机视觉文献中也得到了验证，例如视觉Transformer和注意力增强CNN的开发 10。

### **2.2 推荐架构：混合卷积注意力网络（HCAN）**

* **整体结构**：推荐采用一个类U-Net的编码器-解码器架构。编码器通过一系列卷积块对输入信道矩阵进行下采样，提取日益抽象的特征。解码器则使用转置卷积对这些特征进行上采样，最终生成U、V矩阵和s向量。这种结构非常适合于图像到图像（或本例中的矩阵到矩阵）的任务。  
* **注意力机制集成**：在U-Net的每个卷积块中集成**卷积块注意力模块（Convolutional Block Attention Module, CBAM）** 12。CBAM依次应用通道注意力（关注“什么”特征是重要的）和空间注意力（关注“哪里”的特征是重要的），使网络能够自适应地优化其特征图，聚焦于信道矩阵中最显著的部分，同时抑制噪声。这是对2.1节中混合模型思路的具体实现。  
* **输出头**：解码器将设置三个独立的输出头，分别对应U、V和s，使网络能为SVD的每个组成部分学习专门的映射。

### **2.3 解决正交性难题：李群参数化**

依赖AE损失函数中的正交性惩罚项是一种“尽力而为”的方法。它不能保证最终的U和V是完美的酉矩阵，并且如1.2节所讨论，它使损失函数的优化变得复杂。

因此，推荐采用一种“硬约束”方法，通过参数化U和V的输出层，使其在结构上必然是酉矩阵。根据Lezcano-Casado等人的研究 14，这可以通过

**矩阵指数**实现。

* **实现方式**：网络不再直接输出矩阵U，而是输出一个**斜埃尔米特矩阵**（skew-Hermitian matrix）A（满足A∗=−A）。最终的输出U则通过$U \= \\exp(A)$计算得出。斜埃尔米特矩阵的指数必然是一个酉矩阵。这一变换将一个在施蒂费尔流形（Stiefel Manifold）上的约束优化问题，转化为了一个在斜埃尔米特矩阵构成的欧几里得空间上的无约束优化问题，完美适配标准的梯度下降优化器 15。这可以作为一个自定义的PyTorch层来实现。

采用这种硬约束会产生一系列的战略优势：

1. 它能以机器精度保证满足竞赛的正交性要求。  
2. 它从AE损失函数中移除了两个正交性惩罚项。  
3. 这将问题从一个困难的三目标MTL问题，简化为了一个更为简单的单目标回归问题。  
4. 这种简化使得训练过程更加稳定，并允许模型将其全部能力集中于核心任务——从噪声输入中精确重构理想信道。这是一个通过综合不同领域研究而得出的、强大且非显而易见的战略选择。

#### **表1：正交性强制方法对比分析**

| 特性 | 软约束（正则化） | 硬约束（参数化） |
| :---- | :---- | :---- |
| **正交性保证** | 近似保证，依赖于正则化强度$\\lambda$ | 数学上严格保证（机器精度） |
| **实现复杂度** | 简单，在损失函数中添加一项即可 | 中等，需要实现自定义网络层（如矩阵指数） |
| **训练稳定性** | 可能不稳定，多目标损失梯度可能相互干扰 | 更稳定，简化为单目标回归问题 |
| **对损失函数的影响** | 增加两个惩罚项，构成多任务学习问题 | 移除两个惩罚项，简化损失函数 |
| **计算开销** | 训练时略有增加，推理时无开销 | 训练和推理时均有额外开销（如矩阵指数计算），但可通过技巧优化 15 |
| **参考文献** | 3 | 14 |

---

## **第三部分：高级训练与鲁棒性增强策略**

本节详述如何有效训练所提出的HCAN架构，以满足竞赛对“鲁棒性”和“准确性”的要求。

### **3.1 动态损失平衡框架（备选/后备策略）**

本小节将作为在硬正交性约束实现困难时的备选方案。如果必须依赖完整的三项AE损失函数，就需要一种更精巧的方式来平衡各项损失。

推荐采用Kendall等人提出的基于不确定性的加权方法 16。总损失函数可以被建模为：

Ltotal​=σ12​1​Lrecon​+σ22​1​Lortho\_U​+σ32​1​Lortho\_V​+log(σ1​)+log(σ2​)+log(σ3​)  
其中，σi​是代表每个任务不确定性（或噪声）的可学习参数。网络通过学习来降低噪声较大、难度较高的任务的权重，以防止其梯度淹没整个学习过程，而$\\log(\\sigma\_i)$项则防止权重无限增大（损失趋于零）。可以提供一个自定义PyTorch损失模块的伪代码来实现此功能 18。该方法直接优化了模型对各子任务信心的代理指标，通过学习平衡损失，模型隐式地学习生成一个在AE指标所有组成部分上都表现良好的整体最优解。

### **3.2 通过物理信息增强的数据增强培育鲁棒性**

竞赛明确要求模型对“噪声、硬件非理性因素、信道估计误差等”具有鲁棒性 1。来自计算机视觉领域的通用数据增强方法（如翻转、旋转）对信道矩阵而言缺乏物理意义 20。因此，必须采用基于物理信道特性的数据增强策略。

推荐建立一个数据增强流水线，在提供的$H\_{\\text{label}}$数据上模拟真实的物理损伤，以生成一个更丰富、更鲁棒的训练集。

* **1\. 加性复高斯白噪声（AWGN）**：这是最基础的增强，用于模拟接收机的热噪声。  
* **2\. 相位噪声模拟**：将本地振荡器的相位噪声建模为维纳过程（随机游走），并将其作为时变的相位旋转应用于信道系数。可参考21中的建模方法。  
* **3\. 多普勒频移模拟**：对于给定的静态信道Hlabel​，通过引入多普勒频移来模拟用户移动。这涉及假设一个速度向量，并对每个路径施加特定的相移ej2πfd​t，其中多普勒频率fd​是路径到达角和用户速度的函数。这直接模拟了无线信道一个关键的时变特性 23。  
* **4\. （高级）基于GAN的生成式增强**：作为一种更先进的技术，可以考虑使用条件生成对抗网络（cGAN）来学习所提供信道数据的内在分布 25。cGAN随后可以生成全新的、统计上与训练集相似的合成信道矩阵，从而极大地扩展数据集的规模和多样性 27。

核心任务是从一个带噪的$H\_{\\text{input}}映射到干净的H\_{\\text{label}}的SVD。通过创建一个庞大的训练集，其中H\_{\\text{input}}是H\_{\\text{label}}$经过随机物理损伤增强后的版本，我们明确地训练网络对这些物理损伤具有*不变性*。网络必须学会在分解之前“看穿”噪声、多普勒频移和相位误差，以提取底层的、稳定的信道结构。这种方法以一种有原则的、物理知情的方式直接针对“鲁棒性”要求，远优于简单地添加随机噪声。

#### **表2：推荐的物理信息数据增强技术**

| 增强技术 | 模拟的物理现象 | 关键可变参数 | 对模型鲁棒性的贡献 |
| :---- | :---- | :---- | :---- |
| **AWGN** | 接收机热噪声 | 信噪比（SNR） | 提高对背景噪声的鲁棒性 |
| **相位噪声** | 本地振荡器不稳定性 | 噪声方差、维纳过程步长 | 提高对硬件非理想因素的鲁棒性 |
| **多普勒频移** | 用户/环境移动性 | 用户速度、移动方向 | 提高对时变信道特性的泛化能力 |
| **cGAN** | 信道统计分布 | cGAN架构、训练轮次 | 通过生成多样化的新样本，提高对未知信道类型的泛化能力 |

### **3.3 系统性的超参数优化方法**

* **关键超参数**：需要优化的最关键超参数包括：学习率、批处理大小、优化器选择（如AdamW）、权重衰减，以及架构特定参数，如HCAN中的层数/滤波器数量和CBAM模块中的缩减比r。  
* **调优策略**：推荐采用结构化的搜索方法，如**连续减半（Successive Halving）**（如HalvingGridSearchCV或HalvingRandomSearchCV中所实现 29），它比简单的网格搜索或随机搜索更高效，因为它会将更多计算资源分配给有希望的超参数配置。  
* **正则化特别考量**：如果采用软正交性约束的备选策略，正则化强度$\\lambda将成为一个关键的超参数。\\lambda$过低会导致正交性差，而过高则可能损害重构精度并导致欠拟合 30。该参数必须被纳入超参数搜索空间。

---

## **第四部分：达成效率目标：模型压缩与最终化**

本节遵循1.3节中确定的两阶段优化策略，解决次要但至关重要的MACs指标。

### **4.1 设计即效率：先验MACs削减**

选择HCAN架构本身就是迈向高效率的一步。其卷积特性对于类图像数据通常比纯Transformer的密集注意力图具有更高的MACs效率。架构设计阶段，控制MACs数量的主要杠杆是每个卷积层的滤波器数量和编码器/解码器的层数。这些应作为初始超参数搜索的一部分。

### **4.2 事后优化：结构化剪枝与权重SVD**

如果第一阶段（精度最大化）中性能最佳的模型超出了期望的MACs预算，则需应用训练后压缩技术。

* **结构化剪枝**：推荐采用**结构化剪枝**（如滤波器/通道剪枝）而非非结构化剪枝 32。非结构化剪枝将单个权重置零，产生需要专门硬件才能实现实际加速的稀疏矩阵。而结构化剪枝移除整个滤波器或通道，得到一个更小、更密集的模型，这能直接转化为更低的MACs和在标准硬件上的更快推理速度 33。  
* **权重SVD**：作为一种与主题契合且强大的技术，可以考虑使用SVD来压缩网络中全连接层的权重矩阵，这一方法得到了AIMET 5和近期LLM压缩研究的支持 35。该方法将一个大的权重矩阵  
  W分解为两个更小的矩阵W=Wleft​⋅Wright​，从而减少参数和MACs数量。

推荐的工作流程是：1) 将完整模型训练至收敛。2) 应用剪枝/压缩技术以达到目标压缩比。3) 使用低学习率对压缩后的模型进行几轮**微调**，以恢复在压缩过程中损失的精度 5。

---

## **第五部分：实施路线图与战略建议**

本节提供了一个具体的、分步骤的计划，并总结了赢得比赛的关键战略建议。

### **5.1 分阶段实施与验证计划**

* **第一阶段：基线模型**。实现一个不含注意力或高级损失函数的简单U-Net CNN。在原始数据上训练，以建立性能基准并验证数据加载和评估流程。  
* **第二阶段：引入硬正交性**。为U和V实现基于李群的自定义输出层。这是技术上最具挑战性的部分，但提供了最大的战略优势。验证其输出确实是酉矩阵。  
* **第三阶段：增强架构**。将CBAM模块集成到U-Net中，构建完整的HCAN。  
* **第四阶段：实现数据增强**。构建基于物理信息的数据增强流水线，并在增强后的数据集上重新训练模型，以注入鲁棒性。  
* **第五阶段：超参数调优**。对完整模型进行系统的超参数搜索。  
* **第六阶段：（如必要）模型压缩**。应用结构化剪枝或权重SVD，并进行微调，以满足MACs目标。

### **5.2 最终提交包：最佳实践**

* **代码结构 (solution.py)**：根据竞赛示例文件1的建议，提供一个清晰、模块化的代码结构建议。这包括明确分离网络定义、数据加载、训练循环和推理函数。  
* **模型检查点 (.pth)**：强调不仅要保存最终模型，还要在整个训练过程中根据验证集的表现保存性能最佳的模型。  
* **合规性与最终检查**：提供一个检查清单，确保所有提交组件（.npz输出文件、solution.py、.pth模型）格式正确，并包含在最终的.zip文件中 1。同时提醒参赛者，禁止在解决方案中使用内置的SVD/EVD函数。

## **结论**

本次竞赛的核心挑战在于设计一个能够在非理想、大规模MIMO信道条件下，准确、鲁棒且高效地计算SVD近似值的AI算子。成功的关键不仅在于选择一个强大的神经网络架构，更在于采取一系列相互关联的战略决策。

本报告提出的综合解决方案，其核心优势在于：

1. **架构创新**：通过结合CNN的局部感知能力和注意力机制的全局上下文能力，所提出的HCAN架构在理论上优于单一范式模型。  
2. **数学严谨性**：采用基于李群的硬正交性约束，从根本上解决了传统软约束方法带来的训练不稳定和精度损失问题，并将复杂的多目标优化简化为单目标回归，这是一个决定性的战略优势。  
3. **物理知情性**：通过模拟真实世界中的物理损伤（如相位噪声和多普勒频移）来进行数据增强，使模型能够学习对这些干扰的不变性，从而实现真正的鲁棒性，而不仅仅是统计上的泛化。  
4. **系统工程化**：提出的两阶段优化流程（先保精度，后求效率）和分步实施路线图，为应对精度（AE）和效率（MACs）的双重约束提供了务实且风险可控的路径。

最终，能够胜出的方案将是那些不仅在算法层面有所创新，而且深刻理解无线通信的物理现实，并能将这种理解转化为具体、可执行的工程策略的方案。本报告提供的蓝图旨在为实现这一目标提供一条清晰、有力的指引。

#### **引用的著作**

1. 2025年-AI使能的无线鲁棒SVD算子.docx  
2. Deep Learning for SVD and Hybrid Beamforming \- The University of ..., 访问时间为 七月 19, 2025， [https://uweb.engr.arizona.edu/\~tandonr/journal-papers/TWC-2020-SVD-Hybrid-BF-DL.pdf](https://uweb.engr.arizona.edu/~tandonr/journal-papers/TWC-2020-SVD-Hybrid-BF-DL.pdf)  
3. Why is it so hard to enforce a weight matrix to be orthogonal? \- PyTorch Forums, 访问时间为 七月 19, 2025， [https://discuss.pytorch.org/t/why-is-it-so-hard-to-enforce-a-weight-matrix-to-be-orthogonal/108888](https://discuss.pytorch.org/t/why-is-it-so-hard-to-enforce-a-weight-matrix-to-be-orthogonal/108888)  
4. Orthogonal regularizers in deep learning: how to handle rectangular matrices? \- Université catholique de Louvain, 访问时间为 七月 19, 2025， [https://perso.uclouvain.be/estelle.massart/documents/papier\_ICPR\_orthogonal.pdf](https://perso.uclouvain.be/estelle.massart/documents/papier_ICPR_orthogonal.pdf)  
5. AIMET Compression Features Guidebook — AI Model Efficiency Toolkit Documentation \- Qualcomm Innovation Center, 访问时间为 七月 19, 2025， [https://quic.github.io/aimet-pages/releases/1.31.0/user\_guide/compression\_feature\_guidebook.html](https://quic.github.io/aimet-pages/releases/1.31.0/user_guide/compression_feature_guidebook.html)  
6. Structurally Compress Neural Network DPD Using Projection \- MATLAB & \- MathWorks, 访问时间为 七月 19, 2025， [https://www.mathworks.com/help/comm/ug/structurally-compress-neural-network-dpd-using-projection.html](https://www.mathworks.com/help/comm/ug/structurally-compress-neural-network-dpd-using-projection.html)  
7. Linear algebra with transformers | Request PDF \- ResearchGate, 访问时间为 七月 19, 2025， [https://www.researchgate.net/publication/356800102\_Linear\_algebra\_with\_transformers](https://www.researchgate.net/publication/356800102_Linear_algebra_with_transformers)  
8. Linear algebra with transformers, 访问时间为 七月 19, 2025， [https://arxiv.org/pdf/2112.01898](https://arxiv.org/pdf/2112.01898)  
9. Emulating the Attention Mechanism in Transformer Models with a Fully Convolutional Network | NVIDIA Technical Blog, 访问时间为 七月 20, 2025， [https://developer.nvidia.com/blog/emulating-the-attention-mechanism-in-transformer-models-with-a-fully-convolutional-network/](https://developer.nvidia.com/blog/emulating-the-attention-mechanism-in-transformer-models-with-a-fully-convolutional-network/)  
10. CNNtention: Can CNNs do better with Attention? \- arXiv, 访问时间为 七月 20, 2025， [https://arxiv.org/html/2412.11657v1](https://arxiv.org/html/2412.11657v1)  
11. Convolutional Neural Network with Attention Mechanism and Visual Vibration Signal Analysis for Bearing Fault Diagnosis \- MDPI, 访问时间为 七月 20, 2025， [https://www.mdpi.com/1424-8220/24/6/1831](https://www.mdpi.com/1424-8220/24/6/1831)  
12. CBAM: Convolutional Block Attention Module \- CVF Open Access, 访问时间为 七月 20, 2025， [https://openaccess.thecvf.com/content\_ECCV\_2018/papers/Sanghyun\_Woo\_Convolutional\_Block\_Attention\_ECCV\_2018\_paper.pdf](https://openaccess.thecvf.com/content_ECCV_2018/papers/Sanghyun_Woo_Convolutional_Block_Attention_ECCV_2018_paper.pdf)  
13. arXiv:1807.06521v2 \[cs.CV\] 18 Jul 2018, 访问时间为 七月 20, 2025， [https://arxiv.org/pdf/1807.06521](https://arxiv.org/pdf/1807.06521)  
14. Cheap Orthogonal Constraints in Neural Networks: A Simple Parametrization of the Orthogonal and Unitary Group \- Proceedings of Machine Learning Research, 访问时间为 七月 19, 2025， [http://proceedings.mlr.press/v97/lezcano-casado19a/lezcano-casado19a.pdf](http://proceedings.mlr.press/v97/lezcano-casado19a/lezcano-casado19a.pdf)  
15. Cheap Orthogonal Constraints in Neural Networks: A Simple ... \- arXiv, 访问时间为 七月 19, 2025， [https://arxiv.org/pdf/1901.08428](https://arxiv.org/pdf/1901.08428)  
16. Multi-Task Learning Using Uncertainty to Weigh Losses for Scene Geometry and Semantics \- CVF Open Access, 访问时间为 七月 20, 2025， [https://openaccess.thecvf.com/content\_cvpr\_2018/papers/Kendall\_Multi-Task\_Learning\_Using\_CVPR\_2018\_paper.pdf](https://openaccess.thecvf.com/content_cvpr_2018/papers/Kendall_Multi-Task_Learning_Using_CVPR_2018_paper.pdf)  
17. Loss Weighting in Multi-Task Language Learning \- Stanford University, 访问时间为 七月 20, 2025， [https://web.stanford.edu/class/archive/cs/cs224n/cs224n.1244/final-projects/AnnaLittle.pdf](https://web.stanford.edu/class/archive/cs/cs224n/cs224n.1244/final-projects/AnnaLittle.pdf)  
18. How to learn the weights between two losses? \- PyTorch Forums, 访问时间为 七月 20, 2025， [https://discuss.pytorch.org/t/how-to-learn-the-weights-between-two-losses/39681](https://discuss.pytorch.org/t/how-to-learn-the-weights-between-two-losses/39681)  
19. Mikoto10032/AutomaticWeightedLoss: Multi-task learning using uncertainty to weigh losses for scene geometry and semantics, Auxiliary Tasks in Multi-task Learning \- GitHub, 访问时间为 七月 20, 2025， [https://github.com/Mikoto10032/AutomaticWeightedLoss](https://github.com/Mikoto10032/AutomaticWeightedLoss)  
20. 36 Training for Robustness and Generality \- Foundations of Computer Vision, 访问时间为 七月 19, 2025， [https://visionbook.mit.edu/data\_augmentation.html](https://visionbook.mit.edu/data_augmentation.html)  
21. Phase noise analysis for mmwave massive MIMO: a design framework for scaling via tiled architectures, 访问时间为 七月 20, 2025， [https://wcsl.ece.ucsb.edu/sites/default/files/publications/phase\_noise.pdf](https://wcsl.ece.ucsb.edu/sites/default/files/publications/phase_noise.pdf)  
22. Joint Estimation of Channel and Oscillator Phase Noise in MIMO Systems \- ResearchGate, 访问时间为 七月 20, 2025， [https://www.researchgate.net/publication/258657980\_Joint\_Estimation\_of\_Channel\_and\_Oscillator\_Phase\_Noise\_in\_MIMO\_Systems](https://www.researchgate.net/publication/258657980_Joint_Estimation_of_Channel_and_Oscillator_Phase_Noise_in_MIMO_Systems)  
23. Adaptation of Range-Doppler Algorithm for Efficient Beamforming of Monostatic and Multistatic Ultrasound Signals \- PMC, 访问时间为 七月 20, 2025， [https://pmc.ncbi.nlm.nih.gov/articles/PMC9815947/](https://pmc.ncbi.nlm.nih.gov/articles/PMC9815947/)  
24. Hybrid TOA/AOD/Doppler-Shift Localization Algorithm for NLOS Environments \- Eurecom, 访问时间为 七月 20, 2025， [https://www.eurecom.fr/publication/2889/download/cm-publi-2889.pdf](https://www.eurecom.fr/publication/2889/download/cm-publi-2889.pdf)  
25. CGAN-Based Data Augmentation for Enhanced Channel Prediction in Massive MIMO under Subway Tunnels \- ResearchGate, 访问时间为 七月 19, 2025， [https://www.researchgate.net/publication/390579234\_CGAN-Based\_Data\_Augmentation\_for\_Enhanced\_Channel\_Prediction\_in\_Massive\_MIMO\_under\_Subway\_Tunnels](https://www.researchgate.net/publication/390579234_CGAN-Based_Data_Augmentation_for_Enhanced_Channel_Prediction_in_Massive_MIMO_under_Subway_Tunnels)  
26. GAN-based Massive MIMO Channel Model Trained on Measured Data \- arXiv, 访问时间为 七月 19, 2025， [https://arxiv.org/pdf/2403.05321](https://arxiv.org/pdf/2403.05321)  
27. What are GANs, and how do they help in data augmentation? \- Milvus, 访问时间为 七月 19, 2025， [https://milvus.io/ai-quick-reference/what-are-gans-and-how-do-they-help-in-data-augmentation](https://milvus.io/ai-quick-reference/what-are-gans-and-how-do-they-help-in-data-augmentation)  
28. Conditional-GAN Based Data Augmentation for Deep Learning Task Classifier Improvement Using fNIRS Data \- Frontiers, 访问时间为 七月 19, 2025， [https://www.frontiersin.org/journals/big-data/articles/10.3389/fdata.2021.659146/full](https://www.frontiersin.org/journals/big-data/articles/10.3389/fdata.2021.659146/full)  
29. 3.2. Tuning the hyper-parameters of an estimator — scikit-learn 1.7.1 documentation, 访问时间为 七月 20, 2025， [https://scikit-learn.org/stable/modules/grid\_search.html](https://scikit-learn.org/stable/modules/grid_search.html)  
30. Deep Learning: Hyperparameter tuning, Regularization and Optimization \- Medium, 访问时间为 七月 20, 2025， [https://medium.com/@krushnakr9/deep-learning-hyperparameter-tuning-regularization-and-optimization-e1a8a9ba532b](https://medium.com/@krushnakr9/deep-learning-hyperparameter-tuning-regularization-and-optimization-e1a8a9ba532b)  
31. Can We Gain More from Orthogonality Regularizations in Training Deep Networks?, 访问时间为 七月 20, 2025， [http://papers.neurips.cc/paper/7680-can-we-gain-more-from-orthogonality-regularizations-in-training-deep-networks.pdf](http://papers.neurips.cc/paper/7680-can-we-gain-more-from-orthogonality-regularizations-in-training-deep-networks.pdf)  
32. 4 Popular Model Compression Techniques Explained \- Xailient, 访问时间为 七月 19, 2025， [https://xailient.com/blog/4-popular-model-compression-techniques-explained/](https://xailient.com/blog/4-popular-model-compression-techniques-explained/)  
33. AMC: Automated Model Compression and Acceleration with Reinforcement Learning \- CVF Open Access, 访问时间为 七月 19, 2025， [https://openaccess.thecvf.com/content\_ECCV\_2018/papers/Yihui\_He\_AMC\_Automated\_Model\_ECCV\_2018\_paper.pdf](https://openaccess.thecvf.com/content_ECCV_2018/papers/Yihui_He_AMC_Automated_Model_ECCV_2018_paper.pdf)  
34. \[2409.02134\] Edge AI: Evaluation of Model Compression Techniques for Convolutional Neural Networks \- arXiv, 访问时间为 七月 19, 2025， [https://arxiv.org/abs/2409.02134](https://arxiv.org/abs/2409.02134)  
35. svd-llm: truncation-aware singular value decomposition for large language model compression \- arXiv, 访问时间为 七月 20, 2025， [https://arxiv.org/pdf/2403.07378](https://arxiv.org/pdf/2403.07378)