{"data_dir": ".", "device": "cpu", "round_idx": 1, "matrix_dim": 64, "matrix_rank": 32, "learning_rate": 0.001, "weight_decay": 0.0001, "num_epochs": 50, "batch_size": 16, "augmentation_factor": 3, "use_hard_orthogonality": true, "save_dir": "./checkpoints", "compression_ratio": 0.3, "use_structured_pruning": true, "use_weight_svd": true, "fine_tune_compressed": true, "fine_tune_epochs": 5, "fine_tune_lr": 0.0001, "augmentation_config": {"awgn_snr_range": [15.0, 30.0], "phase_noise_var_range": [0.001, 0.01], "doppler_max_freq": 100.0, "augmentation_prob": 0.8}, "training_config": {"early_stopping_patience": 15, "gradient_clip_norm": 1.0, "scheduler_patience": 10, "scheduler_factor": 0.5}}