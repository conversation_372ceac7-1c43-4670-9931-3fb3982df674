2025-07-23 00:59:45,675 - root - INFO - Starting AI-enabled SVD operator training
2025-07-23 00:59:45,675 - root - INFO - Configuration: {
  "data_dir": ".",
  "device": "cpu",
  "round_idx": 1,
  "matrix_dim": 64,
  "matrix_rank": 32,
  "learning_rate": 0.001,
  "weight_decay": 0.0001,
  "num_epochs": 50,
  "batch_size": 16,
  "augmentation_factor": 3,
  "use_hard_orthogonality": true,
  "save_dir": "./checkpoints",
  "compression_ratio": 0.3,
  "use_structured_pruning": true,
  "use_weight_svd": true,
  "fine_tune_compressed": true,
  "fine_tune_epochs": 5,
  "fine_tune_lr": 0.0001,
  "augmentation_config": {
    "awgn_snr_range": [
      15.0,
      30.0
    ],
    "phase_noise_var_range": [
      0.001,
      0.01
    ],
    "doppler_max_freq": 100.0,
    "augmentation_prob": 0.8
  },
  "training_config": {
    "early_stopping_patience": 15,
    "gradient_clip_norm": 1.0,
    "scheduler_patience": 10,
    "scheduler_factor": 0.5
  }
}
2025-07-23 00:59:45,675 - root - INFO - Phase 1: Training baseline HCAN model for maximum accuracy
2025-07-23 00:59:49,596 - root - INFO - Loaded data: Train shape torch.Size([60000, 64, 64, 2]), Label shape torch.Size([60000, 64, 64, 2])
2025-07-23 00:59:52,189 - root - ERROR - Training failed with error: ReduceLROnPlateau.__init__() got an unexpected keyword argument 'verbose'
2025-07-23 01:00:27,455 - root - INFO - Starting AI-enabled SVD operator training
2025-07-23 01:00:27,455 - root - INFO - Configuration: {
  "data_dir": ".",
  "device": "cpu",
  "round_idx": 1,
  "matrix_dim": 64,
  "matrix_rank": 32,
  "learning_rate": 0.001,
  "weight_decay": 0.0001,
  "num_epochs": 50,
  "batch_size": 16,
  "augmentation_factor": 3,
  "use_hard_orthogonality": true,
  "save_dir": "./checkpoints",
  "compression_ratio": 0.3,
  "use_structured_pruning": true,
  "use_weight_svd": true,
  "fine_tune_compressed": true,
  "fine_tune_epochs": 5,
  "fine_tune_lr": 0.0001,
  "augmentation_config": {
    "awgn_snr_range": [
      15.0,
      30.0
    ],
    "phase_noise_var_range": [
      0.001,
      0.01
    ],
    "doppler_max_freq": 100.0,
    "augmentation_prob": 0.8
  },
  "training_config": {
    "early_stopping_patience": 15,
    "gradient_clip_norm": 1.0,
    "scheduler_patience": 10,
    "scheduler_factor": 0.5
  }
}
2025-07-23 01:00:27,456 - root - INFO - Phase 1: Training baseline HCAN model for maximum accuracy
2025-07-23 01:00:30,546 - root - INFO - Loaded data: Train shape torch.Size([60000, 64, 64, 2]), Label shape torch.Size([60000, 64, 64, 2])
2025-07-23 01:00:32,748 - training - INFO - Augmenting training data...
2025-07-23 01:01:32,982 - training - INFO - Training data: 144000 samples
2025-07-23 01:01:32,993 - training - INFO - Validation data: 6000 samples
2025-07-23 01:01:33,330 - root - ERROR - Training failed with error: The size of tensor a (32) must match the size of tensor b (64) at non-singleton dimension 2
2025-07-23 01:02:08,833 - root - INFO - Starting AI-enabled SVD operator training
2025-07-23 01:02:08,833 - root - INFO - Configuration: {
  "data_dir": ".",
  "device": "cpu",
  "round_idx": 1,
  "matrix_dim": 64,
  "matrix_rank": 32,
  "learning_rate": 0.001,
  "weight_decay": 0.0001,
  "num_epochs": 50,
  "batch_size": 16,
  "augmentation_factor": 3,
  "use_hard_orthogonality": true,
  "save_dir": "./checkpoints",
  "compression_ratio": 0.3,
  "use_structured_pruning": true,
  "use_weight_svd": true,
  "fine_tune_compressed": true,
  "fine_tune_epochs": 5,
  "fine_tune_lr": 0.0001,
  "augmentation_config": {
    "awgn_snr_range": [
      15.0,
      30.0
    ],
    "phase_noise_var_range": [
      0.001,
      0.01
    ],
    "doppler_max_freq": 100.0,
    "augmentation_prob": 0.8
  },
  "training_config": {
    "early_stopping_patience": 15,
    "gradient_clip_norm": 1.0,
    "scheduler_patience": 10,
    "scheduler_factor": 0.5
  }
}
2025-07-23 01:02:08,833 - root - INFO - Phase 1: Training baseline HCAN model for maximum accuracy
2025-07-23 01:02:11,544 - root - INFO - Loaded data: Train shape torch.Size([60000, 64, 64, 2]), Label shape torch.Size([60000, 64, 64, 2])
2025-07-23 01:02:13,840 - training - INFO - Augmenting training data...
2025-07-23 01:03:29,560 - training - INFO - Training data: 144000 samples
2025-07-23 01:03:29,571 - training - INFO - Validation data: 6000 samples
2025-07-23 01:03:29,967 - root - ERROR - Training failed with error: The size of tensor a (32) must match the size of tensor b (64) at non-singleton dimension 2
